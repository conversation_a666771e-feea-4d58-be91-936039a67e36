from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from .config import settings

from cortexacommon.logging import get_logger

logger = get_logger(__name__)

engine = create_async_engine(settings.database_url, echo=True)
AsyncSessionLocal = sessionmaker(bind=engine, class_=AsyncSession, expire_on_commit=False)


async def test_database_connection() -> None:
    """
    Test database connection to ensure it's working properly.
    Follows the fail early principle by testing connectivity during startup.

    Raises:
        Exception: If database connection fails
    """
    logger.info("Testing database connection...")

    try:
        # Test connection using the engine
        async with engine.begin() as conn:
            # Execute a simple query to test connectivity
            result = await conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()

            if test_value != 1:
                raise RuntimeError("Database connection test failed: unexpected result")

        logger.info("Database connection test successful")

    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        raise RuntimeError(f"Failed to connect to database: {e}") from e